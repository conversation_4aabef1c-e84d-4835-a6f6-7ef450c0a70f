@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.9232 0.0026 48.7171);
  --foreground: oklch(0.2795 0.0368 260.031);
  --card: oklch(0.9699 0.0013 106.4238);
  --card-foreground: oklch(0.2795 0.0368 260.031);
  --popover: oklch(0.9699 0.0013 106.4238);
  --popover-foreground: oklch(0.2795 0.0368 260.031);
  --primary: oklch(0.5854 0.2041 277.1173);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.8687 0.0043 56.366);
  --secondary-foreground: oklch(0.4461 0.0263 256.8018);
  --muted: oklch(0.9232 0.0026 48.7171);
  --muted-foreground: oklch(0.551 0.0234 264.3637);
  --accent: oklch(0.9376 0.026 321.9388);
  --accent-foreground: oklch(0.3729 0.0306 259.7328);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.8687 0.0043 56.366);
  --input: oklch(0.8687 0.0043 56.366);
  --ring: oklch(0.5854 0.2041 277.1173);
  --chart-1: oklch(0.5854 0.2041 277.1173);
  --chart-2: oklch(0.5106 0.2301 276.9656);
  --chart-3: oklch(0.4568 0.2146 277.0229);
  --chart-4: oklch(0.3984 0.1773 277.3662);
  --chart-5: oklch(0.3588 0.1354 278.6973);
  --sidebar: oklch(0.8687 0.0043 56.366);
  --sidebar-foreground: oklch(0.2795 0.0368 260.031);
  --sidebar-primary: oklch(0.5854 0.2041 277.1173);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.9376 0.026 321.9388);
  --sidebar-accent-foreground: oklch(0.3729 0.0306 259.7328);
  --sidebar-border: oklch(0.8687 0.0043 56.366);
  --sidebar-ring: oklch(0.5854 0.2041 277.1173);
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 1.25rem;
  --shadow-2xs: 2px 2px 10px 4px hsl(240 4% 60% / 0.09);
  --shadow-xs: 2px 2px 10px 4px hsl(240 4% 60% / 0.09);
  --shadow-sm:
    2px 2px 10px 4px hsl(240 4% 60% / 0.18),
    2px 1px 2px 3px hsl(240 4% 60% / 0.18);
  --shadow:
    2px 2px 10px 4px hsl(240 4% 60% / 0.18),
    2px 1px 2px 3px hsl(240 4% 60% / 0.18);
  --shadow-md:
    2px 2px 10px 4px hsl(240 4% 60% / 0.18),
    2px 2px 4px 3px hsl(240 4% 60% / 0.18);
  --shadow-lg:
    2px 2px 10px 4px hsl(240 4% 60% / 0.18),
    2px 4px 6px 3px hsl(240 4% 60% / 0.18);
  --shadow-xl:
    2px 2px 10px 4px hsl(240 4% 60% / 0.18),
    2px 8px 10px 3px hsl(240 4% 60% / 0.18);
  --shadow-2xl: 2px 2px 10px 4px hsl(240 4% 60% / 0.45);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2244 0.0074 67.437);
  --foreground: oklch(0.9288 0.0126 255.5078);
  --card: oklch(0.2801 0.008 59.3379);
  --card-foreground: oklch(0.9288 0.0126 255.5078);
  --popover: oklch(0.2801 0.008 59.3379);
  --popover-foreground: oklch(0.9288 0.0126 255.5078);
  --primary: oklch(0.6801 0.1583 276.9349);
  --primary-foreground: oklch(0.2244 0.0074 67.437);
  --secondary: oklch(0.3359 0.0077 59.4197);
  --secondary-foreground: oklch(0.8717 0.0093 258.3382);
  --muted: oklch(0.2801 0.008 59.3379);
  --muted-foreground: oklch(0.7137 0.0192 261.3246);
  --accent: oklch(0.3896 0.0074 59.4734);
  --accent-foreground: oklch(0.8717 0.0093 258.3382);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(0.2244 0.0074 67.437);
  --border: oklch(0.3359 0.0077 59.4197);
  --input: oklch(0.3359 0.0077 59.4197);
  --ring: oklch(0.6801 0.1583 276.9349);
  --chart-1: oklch(0.6801 0.1583 276.9349);
  --chart-2: oklch(0.5854 0.2041 277.1173);
  --chart-3: oklch(0.5106 0.2301 276.9656);
  --chart-4: oklch(0.4568 0.2146 277.0229);
  --chart-5: oklch(0.3984 0.1773 277.3662);
  --sidebar: oklch(0.3359 0.0077 59.4197);
  --sidebar-foreground: oklch(0.9288 0.0126 255.5078);
  --sidebar-primary: oklch(0.6801 0.1583 276.9349);
  --sidebar-primary-foreground: oklch(0.2244 0.0074 67.437);
  --sidebar-accent: oklch(0.3896 0.0074 59.4734);
  --sidebar-accent-foreground: oklch(0.8717 0.0093 258.3382);
  --sidebar-border: oklch(0.3359 0.0077 59.4197);
  --sidebar-ring: oklch(0.6801 0.1583 276.9349);
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 1.25rem;
  --shadow-2xs: 2px 2px 10px 4px hsl(0 0% 0% / 0.09);
  --shadow-xs: 2px 2px 10px 4px hsl(0 0% 0% / 0.09);
  --shadow-sm:
    2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18);
  --shadow:
    2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 1px 2px 3px hsl(0 0% 0% / 0.18);
  --shadow-md:
    2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 2px 4px 3px hsl(0 0% 0% / 0.18);
  --shadow-lg:
    2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 4px 6px 3px hsl(0 0% 0% / 0.18);
  --shadow-xl:
    2px 2px 10px 4px hsl(0 0% 0% / 0.18), 2px 8px 10px 3px hsl(0 0% 0% / 0.18);
  --shadow-2xl: 2px 2px 10px 4px hsl(0 0% 0% / 0.45);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none; /* Hide scrollbar for WebKit browsers (Chrome, Safari, Opera) */
  }
  .no-scrollbar {
    -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
    scrollbar-width: none; /* Hide scrollbar for Firefox */
  }
}
