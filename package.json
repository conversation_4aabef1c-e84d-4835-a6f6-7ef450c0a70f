{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/google": "^2.0.6", "@ai-sdk/groq": "^2.0.10", "@ai-sdk/react": "^2.0.15", "@google/generative-ai": "^0.24.1", "@langchain/community": "^0.3.53", "@langchain/core": "^0.3.72", "@langchain/google-genai": "^0.2.16", "@langchain/qdrant": "^0.1.3", "@langchain/textsplitters": "^0.1.0", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "ai": "^5.0.15", "cheerio": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3-dsv": "^2.0.0", "firebase": "^12.1.0", "html-to-text": "^9.0.5", "jsdom": "^26.1.0", "langchain": "^0.3.31", "lucide-react": "^0.540.0", "motion": "^12.23.12", "next": "15.4.7", "next-themes": "^0.4.6", "pdf-parse": "^1.1.1", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.4", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/html-to-text": "^9.0.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.4.7", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}